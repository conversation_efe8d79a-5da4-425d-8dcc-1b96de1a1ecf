#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内存优化版批量处理Parquet文件脚本
专门针对大量文件（如3328个文件）进行优化
"""

import pandas as pd
import os
import glob
from datetime import datetime
from pathlib import Path
import logging
import gc

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('memory_optimized_process.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def process_files_memory_optimized(source_path, batch_size=1000000):
    """
    内存优化的文件处理函数
    
    Args:
        source_path: 源文件路径
        batch_size: 每批处理的记录数，默认100万条
    """
    # 需要提取的字段
    required_columns = [
        'lc_company_id',
        'company_name', 
        'company_status_clean',
        'industry_l1_code',
        'establish_date',
        'cancel_date',
        'revoke_date'
    ]
    
    # 创建输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"processed_company_data_{timestamp}"
    Path(output_dir).mkdir(exist_ok=True)
    logger.info(f"创建输出目录: {output_dir}")
    
    # 获取所有parquet文件
    parquet_files = glob.glob(os.path.join(source_path, "*.parquet"))
    logger.info(f"找到 {len(parquet_files)} 个parquet文件")
    
    if not parquet_files:
        logger.error(f"在路径 {source_path} 下未找到任何parquet文件")
        return
    
    # 初始化变量
    batch_data = []
    batch_count = 0
    total_processed = 0
    current_batch_size = 0
    
    logger.info(f"开始处理 {len(parquet_files)} 个文件...")
    
    try:
        for file_idx, file_path in enumerate(parquet_files, 1):
            if file_idx % 100 == 0:  # 每100个文件输出一次进度
                logger.info(f"进度: {file_idx}/{len(parquet_files)} ({file_idx/len(parquet_files)*100:.1f}%)")
            
            try:
                # 读取文件
                df = pd.read_parquet(file_path, columns=required_columns)
                
                # 检查字段是否存在，添加缺失字段
                for col in required_columns:
                    if col not in df.columns:
                        df[col] = None
                
                # 确保字段顺序一致
                df = df[required_columns]
                
                # 添加到批次数据
                batch_data.append(df)
                current_batch_size += len(df)
                
                # 检查是否需要保存批次
                if current_batch_size >= batch_size:
                    # 合并数据
                    combined_df = pd.concat(batch_data, ignore_index=True)
                    
                    # 处理可能超过批次大小的情况
                    while len(combined_df) >= batch_size:
                        # 保存一个完整批次
                        batch_to_save = combined_df.iloc[:batch_size].copy()
                        batch_count += 1
                        output_file = os.path.join(output_dir, f"batch_{batch_count:03d}.parquet")
                        batch_to_save.to_parquet(output_file, index=False)
                        total_processed += len(batch_to_save)
                        
                        logger.info(f"保存批次 {batch_count}: {len(batch_to_save):,} 条记录 -> batch_{batch_count:03d}.parquet")
                        
                        # 保留剩余数据
                        combined_df = combined_df.iloc[batch_size:].copy()
                        
                        # 强制垃圾回收
                        del batch_to_save
                        gc.collect()
                    
                    # 重置批次数据，保留剩余数据
                    if len(combined_df) > 0:
                        batch_data = [combined_df]
                        current_batch_size = len(combined_df)
                    else:
                        batch_data = []
                        current_batch_size = 0
                    
                    # 清理内存
                    del combined_df
                    gc.collect()
                
                # 清理当前文件数据
                del df
                
                # 每处理1000个文件强制进行一次垃圾回收
                if file_idx % 1000 == 0:
                    gc.collect()
                    logger.info(f"已处理 {file_idx} 个文件，当前累积记录数: {current_batch_size:,}")
                    
            except Exception as e:
                logger.error(f"处理文件 {os.path.basename(file_path)} 时出错: {str(e)}")
                continue
        
        # 处理最后一批数据
        if batch_data and current_batch_size > 0:
            final_batch = pd.concat(batch_data, ignore_index=True)
            batch_count += 1
            output_file = os.path.join(output_dir, f"batch_{batch_count:03d}.parquet")
            final_batch.to_parquet(output_file, index=False)
            total_processed += len(final_batch)
            logger.info(f"保存最终批次 {batch_count}: {len(final_batch):,} 条记录 -> batch_{batch_count:03d}.parquet")
        
        logger.info(f"处理完成！")
        logger.info(f"总共处理 {total_processed:,} 条记录")
        logger.info(f"生成 {batch_count} 个批次文件")
        logger.info(f"输出目录: {output_dir}")
        
    except Exception as e:
        logger.error(f"批量处理过程中发生错误: {str(e)}")
        raise

def main():
    """主函数"""
    # 源数据路径
    source_path = r"F:\蕾奥工作\20.数据库转到本地\output\company_general_info_v3"
    
    # 检查源路径是否存在
    if not os.path.exists(source_path):
        logger.error(f"源路径不存在: {source_path}")
        return
    
    logger.info(f"开始内存优化批量处理")
    logger.info(f"源路径: {source_path}")
    logger.info(f"批次大小: 1,000,000 条记录")
    
    try:
        process_files_memory_optimized(source_path)
    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}")

if __name__ == "__main__":
    main()
