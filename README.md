# Parquet文件批量处理脚本

## 功能说明
批量处理指定目录下的所有Parquet文件，提取指定字段，按100万条记录分批保存。

## 文件说明

### 1. batch_process_parquet.py (完整版)
- **功能**: 完整的批量处理脚本，包含详细的日志记录和错误处理
- **特点**: 
  - 完整的日志系统，同时输出到文件和控制台
  - 详细的错误处理和异常捕获
  - 内存优化的分块读取
  - 进度提示和统计信息

### 2. simple_batch_process.py (简化版)
- **功能**: 简化版批量处理脚本，代码更简洁
- **特点**:
  - 代码简洁，易于理解和修改
  - 基本的错误处理
  - 控制台输出进度信息

## 使用方法

### 环境要求
```bash
pip install pandas pyarrow
```

### 运行脚本
```bash
# 运行完整版
python batch_process_parquet.py

# 或运行简化版
python simple_batch_process.py
```

## 配置参数

### 源数据路径
```python
source_path = r"F:\蕾奥工作\20.数据库转到本地\output\company_general_info_v3"
```

### 提取字段
```python
columns = [
    'lc_company_id',
    'company_name', 
    'company_status_clean',
    'industry_l1_code',
    'establish_date',
    'cancel_date',
    'revoke_date'
]
```

### 批次大小
```python
batch_size = 1000000  # 100万条记录
```

## 输出说明

### 输出目录
- 格式: `processed_company_data_YYYYMMDD_HHMMSS`
- 示例: `processed_company_data_20241129_143022`

### 输出文件
- 格式: `batch_001.parquet`, `batch_002.parquet`, ...
- 每个文件包含最多100万条记录
- 最后一个文件可能少于100万条记录

## 性能优化

1. **分块读取**: 使用50,000条记录的块大小读取文件，避免内存溢出
2. **内存管理**: 及时释放已处理的数据，保持内存使用稳定
3. **批量写入**: 累积到指定数量后批量写入，减少I/O操作

## 注意事项

1. 确保源路径存在且包含parquet文件
2. 确保有足够的磁盘空间存储输出文件
3. 如果源文件中缺少某些字段，脚本会自动添加空值
4. 建议在处理大量数据前先用小样本测试

## 错误处理

- 文件读取错误：跳过有问题的文件，继续处理其他文件
- 字段缺失：自动添加空值，确保输出格式一致
- 内存不足：使用分块读取，控制内存使用

## 日志文件 (仅完整版)
- 文件名: `batch_process.log`
- 包含详细的处理过程和错误信息
- 支持中文编码
